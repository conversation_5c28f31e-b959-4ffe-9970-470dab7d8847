// Library Page JavaScript

// Global variables for library-specific playback
let libraryTrackOrder = [];
let currentLibraryIndex = -1;
let isLibraryMode = false;

document.addEventListener("DOMContentLoaded", () => {
  // Preserve any ongoing playback before loading new state
  if (window.GlobalAudioManager) {
    const playbackState = window.GlobalAudioManager.preservePlayback();
    console.log("Preserved playback state on library page:", playbackState);

    // If music was playing, ensure it continues
    if (playbackState.wasPlaying) {
      console.log("Music was playing - ensuring continuity on library page");
    }
  }

  // Load player state first - this will now respect ongoing playback
  if (typeof loadPlayerState === "function") {
    loadPlayerState();
  }

  // Initialize library functionality
  initializeLibrary();

  // Initialize search functionality for library
  initializeLibrarySearch();

  // Initialize library-specific master player functionality
  initializeLibraryMasterPlayer();
});

// Initialize library functionality
function initializeLibrary() {
  // Get library elements
  const emptyLibrary = document.getElementById("empty-library");
  const librarySongs = document.getElementById("library-songs");

  // If library elements don't exist, return
  if (!emptyLibrary || !librarySongs) return;

  // Get library songs from localStorage
  const librarySongsData = getLibrarySongs();

  // Check if library is empty
  if (librarySongsData.length === 0) {
    // Show empty library message
    emptyLibrary.style.display = "flex";
    librarySongs.style.display = "none";
  } else {
    // Hide empty library message and show songs
    emptyLibrary.style.display = "none";
    librarySongs.style.display = "grid";

    // Populate library songs
    populateLibrarySongs(librarySongsData);
  }
}

// Get library songs from localStorage
function getLibrarySongs() {
  // Use user-specific data manager if available
  if (typeof UserDataManager !== "undefined") {
    return UserDataManager.getUserData("librarySongs", []);
  }

  // Fallback to global localStorage for backward compatibility
  const librarySongs = localStorage.getItem("librarySongs");
  if (librarySongs) {
    return JSON.parse(librarySongs);
  }
  return [];
}

// Save library songs to localStorage
function saveLibrarySongs(songs) {
  // Use user-specific data manager if available
  if (typeof UserDataManager !== "undefined") {
    UserDataManager.setUserData("librarySongs", songs);
  } else {
    // Fallback to global localStorage
    localStorage.setItem("librarySongs", JSON.stringify(songs));
  }
}

// Add song to library
function addSongToLibrary(songId) {
  // Get library songs
  const librarySongs = getLibrarySongs();

  // Check if song already exists in library
  if (librarySongs.includes(songId)) {
    return false; // Song already in library
  }

  // Add song to library
  librarySongs.push(songId);

  // Save updated library
  saveLibrarySongs(librarySongs);

  return true; // Song added successfully
}

// Remove song from library
function removeSongFromLibrary(songId) {
  // Get library songs
  let librarySongs = getLibrarySongs();

  // Remove song from library
  librarySongs = librarySongs.filter((id) => id !== songId);

  // Save updated library
  saveLibrarySongs(librarySongs);

  // If on library page, refresh the display and update track order
  if (window.location.pathname.includes("library.html")) {
    initializeLibrary();
    updateLibraryTrackOrder();
    // Refresh master player context
    if (typeof refreshMasterPlayerContext === "function") {
      refreshMasterPlayerContext();
    }
  }

  return true; // Song removed successfully
}

// Check if song is in library
function isSongInLibrary(songId) {
  // Get library songs
  const librarySongs = getLibrarySongs();

  // Check if song is in library
  return librarySongs.includes(songId);
}

// Populate library songs
function populateLibrarySongs(librarySongIds) {
  // Get library songs container
  const librarySongsContainer = document.getElementById("library-songs");

  // Clear existing songs
  librarySongsContainer.innerHTML = "";

  // Filter songs array to get only library songs
  const libraryTracks = songs.filter((song) =>
    librarySongIds.includes(song.id)
  );

  // Update library track order
  updateLibraryTrackOrder();

  // Add songs to container
  libraryTracks.forEach((song) => {
    const songElement = document.createElement("div");
    songElement.className = "library-song-item";
    songElement.innerHTML = `
      <div class="img_play">
        <img src="${song.poster}" alt="Song Cover">
        <i class="bi playListPlay bi-play-circle-fill" id="${song.id}"></i>
      </div>
      <h5>${extractSongName(song.songName)}</h5>
      <div class="subtitle">${extractArtistName(song.songName)}</div>
      <div class="song-actions">
        <i class="bi bi-heart-fill add-to-library in-library" data-id="${
          song.id
        }" title="Remove from Library"></i>
        <i class="bi bi-trash remove-from-library" data-id="${
          song.id
        }" title="Remove from Library"></i>
      </div>
    `;

    // Add click event to navigate to the song detail page
    songElement.addEventListener("click", (e) => {
      // Only navigate if the click is not on the play button, heart icon, or remove button
      if (
        !e.target.classList.contains("playListPlay") &&
        !e.target.classList.contains("remove-from-library") &&
        !e.target.classList.contains("add-to-library")
      ) {
        window.location.href = `music-detail.html?id=${song.id}&source=library`;
      }
    });

    librarySongsContainer.appendChild(songElement);
  });

  // Add event listeners to play buttons
  Array.from(
    document.querySelectorAll(".library-song-item .playListPlay")
  ).forEach((element) => {
    element.addEventListener("click", (e) => {
      const songId = e.target.id;
      playSong(songId);
    });
  });

  // Add event listeners to heart icons
  Array.from(
    document.querySelectorAll(".library-song-item .add-to-library")
  ).forEach((element) => {
    element.addEventListener("click", (e) => {
      e.stopPropagation(); // Prevent navigation to detail page

      // Check if user is logged in
      if (typeof isUserLoggedIn === "function" && !isUserLoggedIn()) {
        if (typeof showLoginRequiredNotification === "function") {
          showLoginRequiredNotification();
        }
        return;
      }

      const songId = e.target.getAttribute("data-id");

      if (isSongInLibrary(songId)) {
        if (removeSongFromLibrary(songId)) {
          // Sync all heart icons
          if (typeof syncAllHeartIcons === "function") {
            syncAllHeartIcons(songId);
          }
          // Refresh library display since song was removed
          initializeLibrary();
        }
      }
    });
  });

  // Add event listeners to remove buttons
  Array.from(document.querySelectorAll(".remove-from-library")).forEach(
    (element) => {
      element.addEventListener("click", (e) => {
        e.stopPropagation(); // Prevent navigation to detail page
        const songId = e.target.getAttribute("data-id");
        if (removeSongFromLibrary(songId)) {
          // Sync all heart icons
          if (typeof syncAllHeartIcons === "function") {
            syncAllHeartIcons(songId);
          }
          // Refresh library display since song was removed
          initializeLibrary();
        }
      });
    }
  );
}

// Play a song
function playSong(songId) {
  // Find the song in the songs array
  const song = songs.find((song) => song.id === songId);

  if (song) {
    // Update library track order and current index
    updateLibraryTrackOrder();
    currentLibraryIndex = libraryTrackOrder.findIndex((s) => s.id === songId);

    // Use the library-specific play function
    playLibrarySong(song);
  }
}

// Initialize search functionality for library
function initializeLibrarySearch() {
  // Get search elements
  const searchInput = document.querySelector(".search input");

  // If search input doesn't exist, return
  if (!searchInput) return;

  // Add search input event listener
  searchInput.addEventListener("input", () => {
    const searchValue = searchInput.value.toLowerCase();

    // Get all library song items
    const songItems = document.querySelectorAll(".library-song-item");

    // Filter songs based on search value
    songItems.forEach((item) => {
      const songTitle = item.querySelector("h5").textContent.toLowerCase();
      const artistName = item
        .querySelector(".subtitle")
        .textContent.toLowerCase();

      if (songTitle.includes(searchValue) || artistName.includes(searchValue)) {
        item.style.display = "block";
      } else {
        item.style.display = "none";
      }
    });
  });
}

// Extract song name from HTML string
function extractSongName(songNameHtml) {
  // Create a temporary div to parse the HTML
  const tempDiv = document.createElement("div");
  tempDiv.innerHTML = songNameHtml;

  // Get the text content (song name) without the subtitle
  const songName = tempDiv.childNodes[0].textContent.trim();
  return songName;
}

// Extract artist name from HTML string
function extractArtistName(songNameHtml) {
  // Create a temporary div to parse the HTML
  const tempDiv = document.createElement("div");
  tempDiv.innerHTML = songNameHtml;

  // Get the subtitle div content (artist name)
  const artistElement = tempDiv.querySelector(".subtitle");
  return artistElement ? artistElement.textContent.trim() : "Unknown Artist";
}

// Initialize library-specific master player functionality
function initializeLibraryMasterPlayer() {
  // Set library mode flag
  isLibraryMode = true;

  // Update library track order when library is populated
  updateLibraryTrackOrder();

  // Refresh master player context to use library tracks
  if (typeof refreshMasterPlayerContext === "function") {
    refreshMasterPlayerContext();
  }
}

// Update library track order based on current library songs
function updateLibraryTrackOrder() {
  const librarySongIds = getLibrarySongs();

  // Filter songs array to get only library songs in order
  libraryTrackOrder = songs.filter((song) => librarySongIds.includes(song.id));

  // Update current library index if a song is currently playing
  if (index && libraryTrackOrder.length > 0) {
    currentLibraryIndex = libraryTrackOrder.findIndex(
      (song) => song.id === index.toString()
    );
  }
}

// Get next song in library sequence
function getNextLibrarySong() {
  if (libraryTrackOrder.length === 0) return null;

  currentLibraryIndex = (currentLibraryIndex + 1) % libraryTrackOrder.length;
  return libraryTrackOrder[currentLibraryIndex];
}

// Get previous song in library sequence
function getPreviousLibrarySong() {
  if (libraryTrackOrder.length === 0) return null;

  currentLibraryIndex =
    currentLibraryIndex <= 0
      ? libraryTrackOrder.length - 1
      : currentLibraryIndex - 1;
  return libraryTrackOrder[currentLibraryIndex];
}

// Get random song from library
function getRandomLibrarySong() {
  if (libraryTrackOrder.length === 0) return null;

  const randomIndex = Math.floor(Math.random() * libraryTrackOrder.length);
  currentLibraryIndex = randomIndex;
  return libraryTrackOrder[randomIndex];
}

// Get current song in library (for repeat)
function getCurrentLibrarySong() {
  if (libraryTrackOrder.length === 0 || currentLibraryIndex < 0) return null;

  return libraryTrackOrder[currentLibraryIndex];
}

// Note: Navigation override functions removed - now handled by MasterPlayerContext system

// Note: Library-specific navigation functions removed - now handled by MasterPlayerContext system

// Play a song from library with full master player integration
function playLibrarySong(song) {
  if (!song) return;

  // Update current library index
  currentLibraryIndex = libraryTrackOrder.findIndex((s) => s.id === song.id);

  // Use the context system to play the song
  if (window.MasterPlayerContext) {
    window.MasterPlayerContext.playContextualSong(song);
  } else {
    // Fallback to direct play if context system not available
    // Set the current song index
    index = parseInt(song.id);

    // Update the music player
    music.src = `./audio/${index}.mp3`;
    poster_master_play.src = `./styles/images/img/${index}.jpg`;

    // Update the title
    title.innerHTML = song.songName;

    // Update download link
    download_music.href = `./audio/${index}.mp3`;
    download_music.setAttribute("download", extractSongName(song.songName));

    // Play the song
    music.play();

    // Update UI
    masterPlay.classList.remove("bi-play-fill");
    masterPlay.classList.add("bi-pause-fill");

    // Add wave animation
    wave.classList.add("active2");

    // Make all play buttons to play icon
    makeAllPlays();

    // Save current player state
    saveCurrentPlayerState(song.id, 0, true);

    // Add to recently played history
    if (typeof addSongToRecentlyPlayed === "function") {
      addSongToRecentlyPlayed(song.id);
    }

    // Update master heart icon
    if (typeof updateMasterHeartIcon === "function") {
      updateMasterHeartIcon();
    }

    // Update the play button for current song in library
    const playButton = document.getElementById(`${index}`);
    if (playButton) {
      playButton.classList.remove("bi-play-circle-fill");
      playButton.classList.add("bi-pause-circle-fill");
    }
  }
}

// Cleanup function to restore global navigation (called when leaving library page)
function cleanupLibraryMasterPlayer() {
  isLibraryMode = false;
  libraryTrackOrder = [];
  currentLibraryIndex = -1;

  // Remove library-specific music ended event listener
  music.removeEventListener("ended", handleLibraryMusicEnded);

  // Note: Global navigation will be restored when the new page loads
}

// Add cleanup when page is about to unload
window.addEventListener("beforeunload", () => {
  if (isLibraryMode) {
    cleanupLibraryMasterPlayer();
  }
});
