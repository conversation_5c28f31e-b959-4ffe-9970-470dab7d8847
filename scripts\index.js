// Global Audio Manager - Singleton pattern for completely uninterrupted playback
window.GlobalAudioManager =
  window.GlobalAudioManager ||
  (function () {
    let audioInstance = null;
    let isInitialized = false;
    let playbackProtectionActive = false;
    let continuousMonitoringInterval = null;

    return {
      getInstance: function () {
        if (!audioInstance) {
          audioInstance = new Audio();
          audioInstance.volume = 0.3; // Default volume
          audioInstance.preload = "metadata";

          // Prevent audio element from being garbage collected
          window._globalAudioInstance = audioInstance;

          // Add comprehensive event listeners for state management
          audioInstance.addEventListener("loadstart", () => {
            console.log("Audio loading started");
          });

          audioInstance.addEventListener("canplay", () => {
            console.log("Audio can start playing");
          });

          audioInstance.addEventListener("error", (e) => {
            console.error("Audio error:", e);
            this.handleAudioError(e);
          });

          audioInstance.addEventListener("pause", (e) => {
            // Only log if not intentionally paused
            if (playbackProtectionActive) {
              console.warn(
                "Audio paused during protected playback - attempting recovery"
              );
              this.attemptPlaybackRecovery();
            }
          });

          audioInstance.addEventListener("ended", () => {
            // Clear protection when song naturally ends
            playbackProtectionActive = false;
          });

          // Start continuous monitoring for interruptions
          this.startContinuousMonitoring();

          isInitialized = true;
        }
        return audioInstance;
      },

      isInitialized: function () {
        return isInitialized;
      },

      // Enhanced playback preservation with stronger protection
      preservePlayback: function () {
        if (audioInstance) {
          const isPlaying =
            !audioInstance.paused && audioInstance.currentTime > 0;
          const currentTime = audioInstance.currentTime;
          const currentSrc = audioInstance.src;
          const volume = audioInstance.volume;

          // Save current state regardless of playing status
          this.saveCurrentState();

          // If playing, activate maximum protection
          if (isPlaying) {
            console.log(
              "ACTIVATING MAXIMUM PLAYBACK PROTECTION - Music will continue uninterrupted"
            );

            // Activate protection flag
            playbackProtectionActive = true;
            audioInstance._isProtectedPlayback = true;

            // Multiple recovery mechanisms
            this.setupMultipleRecoveryMechanisms();
          }

          return {
            wasPlaying: isPlaying,
            currentTime: currentTime,
            currentSrc: currentSrc,
            volume: volume,
          };
        }
        return { wasPlaying: false };
      },

      // Setup multiple recovery mechanisms for maximum protection
      setupMultipleRecoveryMechanisms: function () {
        // Immediate recovery check
        setTimeout(() => {
          this.attemptPlaybackRecovery();
        }, 50);

        // Secondary recovery check
        setTimeout(() => {
          this.attemptPlaybackRecovery();
        }, 200);

        // Tertiary recovery check
        setTimeout(() => {
          this.attemptPlaybackRecovery();
        }, 500);
      },

      // Attempt to recover playback if interrupted
      attemptPlaybackRecovery: function () {
        if (playbackProtectionActive && audioInstance && audioInstance.paused) {
          console.log("Attempting playback recovery...");
          audioInstance
            .play()
            .then(() => {
              console.log("Playback recovery successful");
            })
            .catch((e) => {
              console.log("Playback recovery failed:", e);
              // Try again after a short delay
              setTimeout(() => {
                if (playbackProtectionActive && audioInstance.paused) {
                  audioInstance.play().catch(() => {});
                }
              }, 1000);
            });
        }
      },

      // Handle audio errors with recovery
      handleAudioError: function (error) {
        console.error("Audio error occurred:", error);
        if (audioInstance && audioInstance.src) {
          const currentSrc = audioInstance.src;
          setTimeout(() => {
            audioInstance.src = currentSrc;
            audioInstance.load();
            if (playbackProtectionActive) {
              audioInstance.play().catch(() => {});
            }
          }, 1000);
        }
      },

      // Start continuous monitoring to prevent any interruptions
      startContinuousMonitoring: function () {
        if (continuousMonitoringInterval) {
          clearInterval(continuousMonitoringInterval);
        }

        continuousMonitoringInterval = setInterval(() => {
          if (
            playbackProtectionActive &&
            audioInstance &&
            audioInstance.paused
          ) {
            console.log(
              "Continuous monitoring detected interruption - recovering"
            );
            this.attemptPlaybackRecovery();
          }
        }, 500); // Check every 500ms
      },

      // Method to check if playback is currently protected
      isPlaybackProtected: function () {
        return (
          playbackProtectionActive ||
          (audioInstance && audioInstance._isProtectedPlayback === true)
        );
      },

      // Method to clear protection (call when intentionally stopping)
      clearPlaybackProtection: function () {
        playbackProtectionActive = false;
        if (audioInstance) {
          audioInstance._isProtectedPlayback = false;
        }
        console.log("Playback protection cleared");
      },

      // Save current playback state
      saveCurrentState: function () {
        if (audioInstance && window.index) {
          const playerState = {
            currentSongId: window.index.toString(),
            currentTime: audioInstance.currentTime,
            isPlaying: !audioInstance.paused,
            volume: audioInstance.volume,
            timestamp: Date.now(),
          };

          // Use user-specific storage if available
          if (typeof UserDataManager !== "undefined") {
            UserDataManager.setUserData("currentPlayerState", playerState);
          } else {
            localStorage.setItem(
              "currentPlayerState",
              JSON.stringify(playerState)
            );
          }
        }
      },

      // Force playback to continue (emergency method)
      forcePlaybackContinuation: function () {
        if (audioInstance && playbackProtectionActive) {
          console.log("Force continuing playback");
          audioInstance.play().catch(() => {});
        }
      },
    };
  })();

// Get the global audio instance
const music = window.GlobalAudioManager.getInstance();

// create Array

// Current player state management
function getCurrentPlayerState() {
  // Use user-specific data manager if available
  if (typeof UserDataManager !== "undefined") {
    return UserDataManager.getUserData("currentPlayerState", {
      currentSongId: null,
      currentTime: 0,
      isPlaying: false,
      volume: 0.3,
    });
  }

  // Fallback to global localStorage for backward compatibility
  const playerState = localStorage.getItem("currentPlayerState");
  if (playerState) {
    return JSON.parse(playerState);
  }
  return {
    currentSongId: null,
    currentTime: 0,
    isPlaying: false,
    volume: 0.3,
  };
}

function saveCurrentPlayerState(songId, currentTime = 0, isPlaying = false) {
  const playerState = {
    currentSongId: songId,
    currentTime: currentTime,
    isPlaying: isPlaying,
    volume: music.volume || 0.3,
    timestamp: Date.now(),
  };

  // Use user-specific storage if available
  if (typeof UserDataManager !== "undefined") {
    UserDataManager.setUserData("currentPlayerState", playerState);
  } else {
    localStorage.setItem("currentPlayerState", JSON.stringify(playerState));
  }

  // Also save through global audio manager
  if (window.GlobalAudioManager) {
    window.GlobalAudioManager.saveCurrentState();
  }
}

// Function to update only UI elements without touching audio playback
function updateUIFromState(state) {
  const index = parseInt(state.currentSongId);

  // Update poster
  if (poster_master_play) {
    poster_master_play.src = `./styles/images/img/${index}.jpg`;
  }

  // Update title
  if (title) {
    let song_title = songs.filter((ele) => ele.id == index);
    song_title.forEach((ele) => {
      title.innerHTML = ele.songName;
    });
  }

  // Update download link
  if (download_music) {
    download_music.href = `./audio/${index}.mp3`;
    let song_title = songs.filter((ele) => ele.id == index);
    song_title.forEach((ele) => {
      if (typeof extractSongName === "function") {
        download_music.setAttribute("download", extractSongName(ele.songName));
      }
    });
  }

  // Update master play button state
  if (masterPlay && state.isPlaying) {
    masterPlay.classList.remove("bi-play-fill");
    masterPlay.classList.add("bi-pause-fill");
    if (wave) wave.classList.add("active2");
  }

  // Update heart icon
  updateMasterHeartIcon();
}

function loadPlayerState() {
  const state = getCurrentPlayerState();
  if (state.currentSongId) {
    // Set the global index to match the saved state
    index = parseInt(state.currentSongId);

    // CRITICAL: Check if music is currently playing and NEVER interrupt it
    if (music) {
      const isCurrentlyPlaying = !music.paused && music.currentTime > 0;
      const isProtected = window.GlobalAudioManager.isPlaybackProtected();

      // ABSOLUTE PRIORITY: Never interrupt ongoing playback
      if (isCurrentlyPlaying || isProtected) {
        console.log(
          "PLAYBACK PROTECTION ACTIVE - Music is playing, only updating UI elements"
        );
        // Only update UI elements, absolutely never touch the audio
        updateUIFromState(state);
        return;
      }

      // Only proceed with audio changes if music is completely stopped
      const expectedSrc = `./audio/${index}.mp3`;
      const currentSrc = music.src;

      // Only change source if it's different and music is completely stopped
      if (!currentSrc.includes(`${index}.mp3`) && music.paused) {
        music.src = expectedSrc;
        console.log("Updated audio source to:", expectedSrc);
      }

      // Set volume (safe to do anytime)
      music.volume = state.volume || 0.3;

      // Set the current time only if music is paused and we have a valid time
      if (music.paused && state.currentTime > 0) {
        music.addEventListener("loadedmetadata", function setTimeOnce() {
          if (music.paused) {
            // Double-check it's still paused
            music.currentTime = state.currentTime;
          }
          music.removeEventListener("loadedmetadata", setTimeOnce);
        });
      }
    }

    // Update UI elements (safe to do anytime)
    if (poster_master_play) {
      poster_master_play.src = `./styles/images/img/${index}.jpg`;
    }

    // Find the song in the songs array
    const song = songs.find((song) => song.id === state.currentSongId);
    if (song) {
      if (title) {
        title.innerHTML = song.songName;
      }
      if (download_music) {
        download_music.href = `./audio/${index}.mp3`;
        download_music.setAttribute("download", extractSongName(song.songName));
      }
    }

    // Update volume slider if it exists
    const volSlider = document.getElementById("vol");
    if (volSlider) {
      volSlider.value = (state.volume || 0.3) * 100;
    }

    // Update UI based on ACTUAL playing state, not saved state
    if (masterPlay && wave) {
      if (!music.paused) {
        // Currently playing
        masterPlay.classList.remove("bi-play-fill");
        masterPlay.classList.add("bi-pause-fill");
        wave.classList.add("active2");
      } else {
        // Currently paused
        masterPlay.classList.add("bi-play-fill");
        masterPlay.classList.remove("bi-pause-fill");
        wave.classList.remove("active2");
      }
    }

    // Update master heart icon
    if (typeof updateMasterHeartIcon === "function") {
      updateMasterHeartIcon();
    }

    // Update play button states based on ACTUAL playing state
    if (typeof makeAllPlays === "function") {
      makeAllPlays();
    }

    // Only update play button if music is actually playing
    if (!music.paused) {
      const playButton = document.getElementById(state.currentSongId);
      if (playButton) {
        playButton.classList.remove("bi-play-circle-fill");
        playButton.classList.add("bi-pause-circle-fill");
      }
    }
  }
}

function extractSongName(songNameHtml) {
  // Extract just the song name without HTML tags
  const tempDiv = document.createElement("div");
  tempDiv.innerHTML = songNameHtml;
  return tempDiv.textContent || tempDiv.innerText || "";
}

// Library functions
function addSongToLibrary(songId) {
  // Get library songs
  const librarySongs = getLibrarySongs();

  // Check if song already exists in library
  if (librarySongs.includes(songId)) {
    return false; // Song already in library
  }

  // Add song to library
  librarySongs.push(songId);

  // Save updated library
  saveLibrarySongs(librarySongs);

  return true; // Song added successfully
}

function removeSongFromLibrary(songId) {
  // Get library songs
  let librarySongs = getLibrarySongs();

  // Remove song from library
  librarySongs = librarySongs.filter((id) => id !== songId);

  // Save updated library
  saveLibrarySongs(librarySongs);

  return true; // Song removed successfully
}

function getLibrarySongs() {
  // Use user-specific data manager if available
  if (typeof UserDataManager !== "undefined") {
    return UserDataManager.getUserData("librarySongs", []);
  }

  // Fallback to global localStorage for backward compatibility
  const librarySongs = localStorage.getItem("librarySongs");
  if (librarySongs) {
    return JSON.parse(librarySongs);
  }
  return [];
}

function saveLibrarySongs(songs) {
  // Use user-specific data manager if available
  if (typeof UserDataManager !== "undefined") {
    UserDataManager.setUserData("librarySongs", songs);
  } else {
    // Fallback to global localStorage
    localStorage.setItem("librarySongs", JSON.stringify(songs));
  }
}

function isSongInLibrary(songId) {
  // Get library songs
  const librarySongs = getLibrarySongs();

  // Check if song is in library
  return librarySongs.includes(songId);
}

// Recently played functions
function addSongToRecentlyPlayed(songId) {
  // Get recently played songs
  let recentlyPlayedSongs = getRecentlyPlayedSongs();

  // Create new entry with timestamp
  const newEntry = {
    songId: songId,
    timestamp: new Date().toISOString(),
    playedAt: Date.now(),
  };

  // Remove existing entry if it exists (to avoid duplicates)
  recentlyPlayedSongs = recentlyPlayedSongs.filter(
    (entry) => entry.songId !== songId
  );

  // Add new entry to the beginning
  recentlyPlayedSongs.unshift(newEntry);

  // Limit to last 50 songs to prevent unlimited storage growth
  if (recentlyPlayedSongs.length > 50) {
    recentlyPlayedSongs = recentlyPlayedSongs.slice(0, 50);
  }

  // Save updated recently played songs
  saveRecentlyPlayedSongs(recentlyPlayedSongs);

  return true; // Song added successfully
}

function getRecentlyPlayedSongs() {
  // Use user-specific data manager if available
  if (typeof UserDataManager !== "undefined") {
    return UserDataManager.getUserData("recentlyPlayedSongs", []);
  }

  // Fallback to global localStorage for backward compatibility
  const recentlyPlayedSongs = localStorage.getItem("recentlyPlayedSongs");
  if (recentlyPlayedSongs) {
    return JSON.parse(recentlyPlayedSongs);
  }
  return [];
}

function saveRecentlyPlayedSongs(songs) {
  // Use user-specific data manager if available
  if (typeof UserDataManager !== "undefined") {
    UserDataManager.setUserData("recentlyPlayedSongs", songs);
  } else {
    // Fallback to global localStorage
    localStorage.setItem("recentlyPlayedSongs", JSON.stringify(songs));
  }
}

const songs = [
  {
    id: "1",
    songName: `On My Way <br>
        <div class="subtitle">Alan Walker</div>`,
    poster: "./styles/images/img/1.jpg",
  },
  {
    id: "2",
    songName: `Faded <br>
        <div class="subtitle">Alan Walker</div>`,
    poster: "./styles/images/img/2.jpg",
  },
  {
    id: "3",
    songName: `Cartoon - On & On <br>
        <div class="subtitle">Daniel Levi</div>`,
    poster: "./styles/images/img/3.jpg",
  },
  {
    id: "4",
    songName: `Warriyo - Mortals <br>
        <div class="subtitle">Laura Brehm</div>`,
    poster: "./styles/images/img/4.jpg",
  },
  {
    id: "5",
    songName: `Ertugrul Gazi <br>
        <div class="subtitle">Ertugrul</div>`,
    poster: "./styles/images/img/5.jpg",
  },
  {
    id: "6",
    songName: `Electronic Music <br>
        <div class="subtitle">Electro</div>`,
    poster: "./styles/images/img/6.jpg",
  },
  {
    id: "7",
    songName: `Agar Tum Sath Ho <br>
        <div class="subtitle">Tamasha</div>`,
    poster: "./styles/images/img/7.jpg",
  },
  {
    id: "8",
    songName: `Suna Hai <br>
        <div class="subtitle">Neha Kakkar</div>`,
    poster: "./styles/images/img/8.jpg",
  },
  {
    id: "9",
    songName: `Dilbar <br>
        <div class="subtitle">Satyameva Jayate</div>`,
    poster: "./styles/images/img/9.jpg",
  },
  {
    id: "10",
    songName: `Duniya <br>
        <div class="subtitle">Luka Chuppi</div>`,
    poster: "./styles/images/img/10.jpg",
  },
  {
    id: "11",
    songName: `Lagdi Lahore Di <br>
        <div class="subtitle">Street Dancer 3D</div>`,
    poster: "./styles/images/img/11.jpg",
  },
  {
    id: "12",
    songName: `Putt Jatt Da <br>
        <div class="subtitle">Putt Jatt Da</div>`,
    poster: "./styles/images/img/12.jpg",
  },
  {
    id: "13",
    songName: `Baarishein <br>
        <div class="subtitle">Atif Aslam</div>`,
    poster: "./styles/images/img/13.jpg",
  },
  {
    id: "14",
    songName: `Vaaste <br>
        <div class="subtitle">Dhvani Bhanushali</div>`,
    poster: "./styles/images/img/14.jpg",
  },
  {
    id: "15",
    songName: `Lut Gaye <br>
        <div class="subtitle">Jubin Nautiyal</div>`,
    poster: "./styles/images/img/15.jpg",
  },
  {
    id: "16",
    songName: `Tu Meri Zindagi Hai <br>
        <div class="subtitle">Jubin Nautiyal</div>`,
    poster: "./styles/images/img/16.jpg",
  },
  {
    id: "17",
    songName: `Suýt Nữa Thì <br>
        <div class="subtitle">Andiez</div>`,
    poster: "./styles/images/img/17.jpg",
  },
  {
    id: "18",
    songName: `Âm Thầm Bên Em <br>
        <div class="subtitle">Sơn Tùng M-TP</div>`,
    poster: "./styles/images/img/18.jpg",
  },
  {
    id: "19",
    songName: `Anh Đã Quen Với Cô Đơn <br>
        <div class="subtitle">Soobin Hoàng Sơn</div>`,
    poster: "./styles/images/img/19.jpg",
  },
  {
    id: "20",
    songName: `Nơi Này Có Anh <br>
        <div class="subtitle">Sơn Tùng M-TP</div>`,
    poster: "./styles/images/img/20.jpg",
  },
  {
    id: "21",
    songName: `Chờ Đợi Có Đáng Sợ <br>
        <div class="subtitle">Andiez</div>`,
    poster: "./styles/images/img/21.jpg",
  },
  {
    id: "22",
    songName: `Chúng Ta Không Thuộc Về Nhau <br>
        <div class="subtitle">Sơn Tùng M-TP</div>`,
    poster: "./styles/images/img/22.jpg",
  },
  {
    id: "23",
    songName: `Cô Ấy Nói <br>
        <div class="subtitle">Ngô Anh Đạt</div>`,
    poster: "./styles/images/img/23.jpg",
  },
  {
    id: "24",
    songName: `Có Chắc Yêu Là Đây <br>
        <div class="subtitle">Sơn Tùng M-TP</div>`,
    poster: "./styles/images/img/24.jpg",
  },
  {
    id: "25",
    songName: `Đoạn Kết Cuối <br>
        <div class="subtitle">Vũ Thịnh, Fanny</div>`,
    poster: "./styles/images/img/25.jpg",
  },
  {
    id: "26",
    songName: `Dưới Những Cơn Mưa <br>
        <div class="subtitle">Mr. Siro</div>`,
    poster: "./styles/images/img/26.jpg",
  },
  {
    id: "27",
    songName: `Đường Tôi Chở Em Về <br>
        <div class="subtitle">Bùi Trường Linh</div>`,
    poster: "./styles/images/img/27.jpg",
  },
  {
    id: "28",
    songName: `Nắng Ấm Xa Dần <br>
        <div class="subtitle">Sơn Tùng M-TP</div>`,
    poster: "./styles/images/img/28.jpg",
  },
  {
    id: "29",
    songName: `Như Anh Đã Thấy Em <br>
        <div class="subtitle">PhucXP, Freak D</div>`,
    poster: "./styles/images/img/29.jpg",
  },
  {
    id: "30",
    songName: `Như Ngày Hôm Qua <br>
        <div class="subtitle">Sơn Tùng M-TP</div>`,
    poster: "./styles/images/img/30.jpg",
  },
  {
    id: "31",
    songName: `Tháng Tư Là Lời Nói Dối Của Em <br>
        <div class="subtitle">Hà Anh Tuấn</div>`,
    poster: "./styles/images/img/31.jpg",
  },
  {
    id: "32",
    songName: `Vở Kịch Của Em <br>
        <div class="subtitle">Hồ Phong An</div>`,
    poster: "./styles/images/img/32.jpg",
  },
  {
    id: "33",
    songName: `Cause I Love You <br>
        <div class="subtitle">Noo Phước Thịnh</div>`,
    poster: "./styles/images/img/33.jpg",
  },
  {
    id: "34",
    songName: `Yêu Người Có Ước Mơ <br>
        <div class="subtitle">Bùi Trường Linh</div>`,
    poster: "./styles/images/img/34.jpg",
  },
];

// Process only sidebar song items (exclude popular songs which are handled separately)
Array.from(document.querySelectorAll(".menu_song .songItem")).forEach(
  (Element, i) => {
    Element.getElementsByTagName("img")[0].src = songs[i].poster;
    Element.getElementsByTagName("h5")[0].innerHTML = songs[i].songName;

    // Add click event to navigate to music detail page
    Element.addEventListener("click", (e) => {
      // Only navigate if the click is not on the play button or library button
      if (
        !e.target.classList.contains("playListPlay") &&
        !e.target.classList.contains("add-to-library")
      ) {
        window.location.href = `music-detail.html?id=${songs[i].id}`;
      }
    });
  }
);

// Search functionality moved to search.js

let masterPlay = document.getElementById("masterPlay");
let wave = document.getElementsByClassName("wave")[0];

masterPlay.addEventListener("click", (e) => {
  e.stopPropagation(); // Prevent event bubbling

  if (music.paused || music.currentTime <= 0) {
    // Clear any protection when user manually starts playback
    if (window.GlobalAudioManager) {
      window.GlobalAudioManager.clearPlaybackProtection();
    }

    music
      .play()
      .then(() => {
        masterPlay.classList.remove("bi-play-fill");
        masterPlay.classList.add("bi-pause-fill");
        wave.classList.add("active2");

        // Activate protection for new playback
        if (window.GlobalAudioManager) {
          window.GlobalAudioManager.preservePlayback();
        }

        // Save current player state as playing
        if (index) {
          saveCurrentPlayerState(index.toString(), music.currentTime, true);
        }
      })
      .catch((error) => {
        console.log("Play prevented:", error);
      });
  } else {
    // Clear protection when user manually pauses
    if (window.GlobalAudioManager) {
      window.GlobalAudioManager.clearPlaybackProtection();
    }

    music.pause();
    masterPlay.classList.add("bi-play-fill");
    masterPlay.classList.remove("bi-pause-fill");
    wave.classList.remove("active2");

    // Save current player state as paused
    if (index) {
      saveCurrentPlayerState(index.toString(), music.currentTime, false);
    }
  }
});

const makeAllPlays = () => {
  Array.from(document.getElementsByClassName("playListPlay")).forEach(
    (Element) => {
      Element.classList.add("bi-play-circle-fill");
      Element.classList.remove("bi-pause-circle-fill");
    }
  );
};
const makeAllBackgrounds = () => {
  Array.from(document.getElementsByClassName("songItem")).forEach((Element) => {
    Element.style.background = "rgb(105, 105, 170, 0)";
  });
};

let index = 0;
let poster_master_play = document.getElementById("poster_master_play");
let download_music = document.getElementById("download_music");
let title = document.getElementById("title");

// Master player heart icon functionality
let masterHeart = document.getElementById("masterHeart");

// Function to update master player heart icon based on current song
function updateMasterHeartIcon(withAnimation = false) {
  if (!masterHeart || !index) return;

  const currentSongId = index.toString();

  if (isSongInLibrary(currentSongId)) {
    masterHeart.classList.remove("bi-heart");
    masterHeart.classList.add("bi-heart-fill");
    masterHeart.title = "Remove from Library";

    // Add animation effect if requested
    if (withAnimation) {
      masterHeart.classList.add("heart-animation");
      setTimeout(() => {
        masterHeart.classList.remove("heart-animation");
      }, 600);
    }
  } else {
    masterHeart.classList.remove("bi-heart-fill");
    masterHeart.classList.add("bi-heart");
    masterHeart.title = "Add to Library";
  }
}

// Global heart synchronization system
function syncAllHeartIcons(songId, triggerAnimation = true) {
  // Update master player heart icon with animation if it's the current song
  const isCurrentSong = index && index.toString() === songId;
  updateMasterHeartIcon(isCurrentSong && triggerAnimation);

  // Update all music card heart icons
  updateMusicCardHeartIcons(songId, triggerAnimation);

  // Update music detail page heart icon if on that page
  updateMusicDetailHeartIcon(songId);

  // Trigger custom event for other components to listen
  window.dispatchEvent(
    new CustomEvent("heartIconSync", {
      detail: {
        songId,
        isInLibrary: isSongInLibrary(songId),
        triggerAnimation: triggerAnimation,
      },
    })
  );
}

// Function to update all music card heart icons for a specific song
function updateMusicCardHeartIcons(songId, triggerAnimation = true) {
  // Update heart icons in all music cards across the page
  const heartIcons = document.querySelectorAll(
    `.add-to-library[data-id="${songId}"], .add-to-library`
  );

  heartIcons.forEach((icon) => {
    // Check if this icon belongs to the current song
    const iconSongId =
      icon.getAttribute("data-id") ||
      icon.closest(".songItem")?.querySelector(".playListPlay")?.id ||
      icon.parentElement?.querySelector(".playListPlay")?.id;

    if (iconSongId === songId) {
      if (isSongInLibrary(songId)) {
        icon.classList.remove("bi-heart");
        icon.classList.add("bi-heart-fill", "in-library");
        icon.title = "Remove from Library";
        // Add animation effect if requested
        if (triggerAnimation) {
          icon.classList.add("heart-animation");
          setTimeout(() => {
            icon.classList.remove("heart-animation");
          }, 600);
        }
      } else {
        icon.classList.remove("bi-heart-fill", "in-library");
        icon.classList.add("bi-heart");
        icon.title = "Add to Library";
      }
    }
  });
}

// Function to update music detail page heart icon
function updateMusicDetailHeartIcon(songId) {
  // This will be called from music-detail.js if on that page
  if (typeof window.updateDetailPageHeartIcon === "function") {
    window.updateDetailPageHeartIcon(songId);
  }
}

// Master heart click handler
if (masterHeart) {
  masterHeart.addEventListener("click", (e) => {
    e.stopPropagation();

    // Check if user is logged in
    if (typeof isUserLoggedIn === "function" && !isUserLoggedIn()) {
      if (typeof showLoginRequiredNotification === "function") {
        showLoginRequiredNotification();
      }
      return;
    }

    if (!index) return;

    const currentSongId = index.toString();

    if (isSongInLibrary(currentSongId)) {
      // Remove from library
      if (removeSongFromLibrary(currentSongId)) {
        // Show notification
        if (typeof showNotification === "function") {
          showNotification("Đã xóa bài hát khỏi thư viện của bạn!", 3000);
        }

        // Sync all heart icons
        syncAllHeartIcons(currentSongId);
      }
    } else {
      // Add to library
      if (addSongToLibrary(currentSongId)) {
        // Show notification
        if (typeof showNotification === "function") {
          showNotification("Thêm vào thư viện thành công", 3000);
        }

        // Sync all heart icons
        syncAllHeartIcons(currentSongId);
      }
    }
  });
}

// Initialize heart icons and event listeners on page load with MAXIMUM PLAYBACK PROTECTION
document.addEventListener("DOMContentLoaded", () => {
  console.log("INITIALIZING UNINTERRUPTED PLAYBACK SYSTEM");

  // Initialize playback preservation system FIRST
  if (window.PlaybackPreservation) {
    window.PlaybackPreservation.init();
  }

  // Initialize master player context
  if (window.MasterPlayerContext) {
    window.MasterPlayerContext.init();
  }

  // Preserve any ongoing playback before loading new state
  if (window.GlobalAudioManager) {
    const playbackState = window.GlobalAudioManager.preservePlayback();
    console.log(
      "MAXIMUM PROTECTION: Preserved playback state on index page:",
      playbackState
    );

    // If music was playing, ensure it continues with maximum protection
    if (playbackState.wasPlaying) {
      console.log(
        "CRITICAL: Music was playing - activating maximum protection"
      );
      // Multiple recovery attempts to ensure continuity
      setTimeout(
        () => window.GlobalAudioManager.forcePlaybackContinuation(),
        100
      );
      setTimeout(
        () => window.GlobalAudioManager.forcePlaybackContinuation(),
        300
      );
      setTimeout(
        () => window.GlobalAudioManager.forcePlaybackContinuation(),
        600
      );
    }
  }

  // Load player state first - this will now respect ongoing playback
  loadPlayerState();

  // Set initial state of master heart icon
  if (index) {
    updateMasterHeartIcon();
  }

  // Initialize heart icons for all music cards
  initializeHeartIcons();

  // Listen for heart sync events
  window.addEventListener("heartIconSync", (event) => {
    const { songId, triggerAnimation } = event.detail;
    updateMusicCardHeartIcons(songId, triggerAnimation);

    // Update search heart icons
    updateSearchHeartIcons(songId, triggerAnimation);

    // Update recently played page heart icons if on that page
    if (window.location.pathname.includes("recently-played.html")) {
      window.updateRecentlyPlayedHeartIcons(songId);
    }

    // Update library page if on that page
    if (window.location.pathname.includes("library.html")) {
      window.updateLibraryHeartIcons(songId);
    }
  });

  // Global function to update recently played heart icons
  window.updateRecentlyPlayedHeartIcons = function (songId) {
    const heartIcons = document.querySelectorAll(
      `.recently-played-song-item .add-to-library[data-id="${songId}"]`
    );
    heartIcons.forEach((icon) => {
      if (isSongInLibrary(songId)) {
        icon.classList.remove("bi-heart");
        icon.classList.add("bi-heart-fill", "in-library");
        icon.title = "Remove from Library";
      } else {
        icon.classList.remove("bi-heart-fill", "in-library");
        icon.classList.add("bi-heart");
        icon.title = "Add to Library";
      }
    });
  };

  // Global function to update library heart icons
  window.updateLibraryHeartIcons = function (songId) {
    const heartIcons = document.querySelectorAll(
      `.library-song-item .add-to-library[data-id="${songId}"]`
    );
    heartIcons.forEach((icon) => {
      if (isSongInLibrary(songId)) {
        icon.classList.remove("bi-heart");
        icon.classList.add("bi-heart-fill", "in-library");
        icon.title = "Remove from Library";
      } else {
        icon.classList.remove("bi-heart-fill", "in-library");
        icon.classList.add("bi-heart");
        icon.title = "Add to Library";
      }
    });
  };

  // Global function to update search heart icons
  function updateSearchHeartIcons(songId, triggerAnimation = true) {
    const heartIcons = document.querySelectorAll(
      `.search-heart[data-id="${songId}"]`
    );
    heartIcons.forEach((icon) => {
      if (isSongInLibrary(songId)) {
        icon.classList.remove("bi-heart");
        icon.classList.add("bi-heart-fill", "in-library");
        icon.title = "Remove from Library";
        // Add animation effect if requested
        if (triggerAnimation) {
          icon.classList.add("heart-animation");
          setTimeout(() => {
            icon.classList.remove("heart-animation");
          }, 600);
        }
      } else {
        icon.classList.remove("bi-heart-fill", "in-library");
        icon.classList.add("bi-heart");
        icon.title = "Add to Library";
      }
    });
  }

  // Add event listeners to save state when music plays/pauses
  music.addEventListener("play", () => {
    if (index) {
      saveCurrentPlayerState(index.toString(), music.currentTime, true);
    }
  });

  music.addEventListener("pause", () => {
    if (index) {
      saveCurrentPlayerState(index.toString(), music.currentTime, false);
    }
  });

  // Save current time periodically while playing
  music.addEventListener("timeupdate", () => {
    if (index && !music.paused) {
      // Save state every 5 seconds to avoid too frequent updates
      const now = Date.now();
      const lastSave = music.lastStateSave || 0;
      if (now - lastSave > 5000) {
        saveCurrentPlayerState(index.toString(), music.currentTime, true);
        music.lastStateSave = now;
      }
    }
  });

  // Add error recovery for audio element
  music.addEventListener("error", (e) => {
    console.error("Audio error occurred:", e);
    // Try to recover by reloading the current source after a delay
    setTimeout(() => {
      if (music.src && index) {
        const currentSrc = music.src;
        music.src = currentSrc;
        music.load();
      }
    }, 1000);
  });

  // Add stalled event handler
  music.addEventListener("stalled", () => {
    console.log("Audio stalled, attempting recovery");
    music.load();
  });

  // Prevent audio element from being modified by other scripts
  Object.defineProperty(window, "music", {
    value: music,
    writable: false,
    configurable: false,
  });

  // Final check: Ensure continuous playback monitoring is active
  if (window.GlobalAudioManager) {
    // Start protection immediately if music is already playing
    const music = window.GlobalAudioManager.getInstance();
    if (music && !music.paused && music.currentTime > 0) {
      console.log(
        "FINAL CHECK: DETECTED ONGOING PLAYBACK - Activating maximum protection"
      );
      window.GlobalAudioManager.preservePlayback();
    }
  }

  console.log("UNINTERRUPTED PLAYBACK SYSTEM FULLY INITIALIZED AND ACTIVE");
});

// Initialize heart icon functionality for music cards
function initializeHeartIcons() {
  // Set initial state for all heart icons
  document.querySelectorAll(".add-to-library").forEach((heartIcon) => {
    const songId = heartIcon.getAttribute("data-id");
    if (songId && isSongInLibrary(songId)) {
      heartIcon.classList.remove("bi-heart");
      heartIcon.classList.add("bi-heart-fill", "in-library");
      heartIcon.title = "Remove from Library";
    }
  });

  // Add click event listeners to all heart icons (avoid duplicates)
  document
    .querySelectorAll(".add-to-library:not([data-heart-initialized])")
    .forEach((heartIcon) => {
      heartIcon.setAttribute("data-heart-initialized", "true");
      heartIcon.addEventListener("click", (e) => {
        e.stopPropagation();

        // Check if user is logged in
        if (typeof isUserLoggedIn === "function" && !isUserLoggedIn()) {
          if (typeof showLoginRequiredNotification === "function") {
            showLoginRequiredNotification();
          }
          return;
        }

        const songId = e.target.getAttribute("data-id");

        if (!songId) return;

        if (isSongInLibrary(songId)) {
          // Remove from library
          if (removeSongFromLibrary(songId)) {
            // Show notification
            if (typeof showNotification === "function") {
              showNotification("Đã xóa bài hát khỏi thư viện của bạn!", 3000);
            }
            // Sync all heart icons
            syncAllHeartIcons(songId);
          }
        } else {
          // Add to library
          if (addSongToLibrary(songId)) {
            // Show notification
            if (typeof showNotification === "function") {
              showNotification("Thêm vào thư viện thành công", 3000);
            }
            // Sync all heart icons
            syncAllHeartIcons(songId);
          }
        }
      });
    });

  // Initialize search heart icons if they exist
  if (typeof initializeSearchHeartIcons === "function") {
    initializeSearchHeartIcons();
  }
}
Array.from(document.getElementsByClassName("playListPlay")).forEach(
  (Element) => {
    Element.addEventListener("click", (e) => {
      index = e.target.id;
      makeAllPlays();
      e.target.classList.remove("bi-play-circle-fill");
      e.target.classList.add("bi-pause-circle-fill");
      music.src = `./audio/${index}.mp3`;
      download_music.href = `./audio/${index}.mp3`;
      poster_master_play.src = `./styles/images/img/${index}.jpg`;

      // Play music and activate protection
      music
        .play()
        .then(() => {
          // Activate playback protection immediately when music starts
          if (window.GlobalAudioManager) {
            window.GlobalAudioManager.preservePlayback();
          }
        })
        .catch((error) => {
          console.log("Play prevented:", error);
        });

      let song_title = songs.filter((ele) => {
        return ele.id == index;
      });

      song_title.forEach((ele) => {
        let { songName } = ele;
        title.innerHTML = songName;
        download_music.setAttribute("download", extractSongName(songName));
      });

      // Save current player state
      saveCurrentPlayerState(index, 0, true);

      // Add to recently played history
      addSongToRecentlyPlayed(index);

      // Update master heart icon
      updateMasterHeartIcon();

      masterPlay.classList.remove("bi-play-fill");
      masterPlay.classList.add("bi-pause-fill");
      wave.classList.add("active2");

      // music.addEventListener('ended', () => {
      //   masterPlay.classList.add("bi-play-fill");
      //   masterPlay.classList.remove("bi-pause-fill");
      //   wave.classList.remove("active2");
      // })
      makeAllBackgrounds();
      Array.from(document.getElementsByClassName("songItem"))[
        `${index - 1}`
      ].style.background = "rgb(105, 105, 170, .1)";
    });
  }
);

let currentStart = document.getElementById("currentStart");
let currentEnd = document.getElementById("currentEnd");
let seek = document.getElementById("seek");
let bar2 = document.getElementById("bar2");
let dot = document.getElementsByClassName("dot")[0];

music.addEventListener("timeupdate", () => {
  let music_curr = music.currentTime;
  let music_dur = music.duration;

  let min = Math.floor(music_dur / 60);
  let sec = Math.floor(music_dur % 60);
  if (sec < 10) {
    sec = `0${sec}`;
  }
  currentEnd.innerText = `${min}:${sec}`;

  let min1 = Math.floor(music_curr / 60);
  let sec1 = Math.floor(music_curr % 60);
  if (sec1 < 10) {
    sec1 = `0${sec1}`;
  }
  currentStart.innerText = `${min1}:${sec1}`;

  let progressbar = parseInt((music.currentTime / music.duration) * 100);
  seek.value = progressbar;
  let seekbar = seek.value;
  bar2.style.width = `${seekbar}%`;
  dot.style.left = `${seekbar}%`;
});

seek.addEventListener("change", () => {
  music.currentTime = (seek.value * music.duration) / 100;
});

const next_music = () => {
  masterPlay.classList.add("bi-pause-fill");
  wave.classList.add("active2");
  if (index == songs.length) {
    index == 0;
  }
  index++;
  music.src = `./audio/${index}.mp3`;
  download_music.href = `./audio/${index}.mp3`;
  poster_master_play.src = `./styles/images/img/${index}.jpg`;

  // Play and maintain protection
  music
    .play()
    .then(() => {
      // Maintain playback protection during track changes
      if (window.GlobalAudioManager) {
        window.GlobalAudioManager.preservePlayback();
      }
    })
    .catch((error) => {
      console.log("Play prevented:", error);
    });

  let song_title = songs.filter((ele) => {
    return ele.id == index;
  });

  song_title.forEach((ele) => {
    let { songName } = ele;
    title.innerHTML = songName;
    download_music.setAttribute("download", extractSongName(songName));
  });
  makeAllBackgrounds();
  Array.from(document.getElementsByClassName("songItem"))[
    `${index - 1}`
  ].style.background = "rgb(105, 105, 170, .1)";
  makeAllPlays();
  document
    .getElementsByClassName("playListPlay")
    [index - 1].classList.remove("bi-play-circle-fill");
  document
    .getElementsByClassName("playListPlay")
    [index - 1].classList.add("bi-pause-circle-fill");

  // Save current player state
  saveCurrentPlayerState(index.toString(), 0, true);

  // Add to recently played history
  addSongToRecentlyPlayed(index);

  // Update master heart icon
  updateMasterHeartIcon();
};

const repeat_music = () => {
  masterPlay.classList.add("bi-pause-fill");
  wave.classList.add("active2");
  index;
  music.src = `./audio/${index}.mp3`;
  download_music.href = `./audio/${index}.mp3`;
  poster_master_play.src = `./styles/images/img/${index}.jpg`;
  music.play();
  let song_title = songs.filter((ele) => {
    return ele.id == index;
  });

  song_title.forEach((ele) => {
    let { songName } = ele;
    title.innerHTML = songName;
    download_music.setAttribute("download", extractSongName(songName));
  });
  makeAllBackgrounds();
  Array.from(document.getElementsByClassName("songItem"))[
    `${index - 1}`
  ].style.background = "rgb(105, 105, 170, .1)";
  makeAllPlays();
  document
    .getElementsByClassName("playListPlay")
    [index - 1].classList.remove("bi-play-circle-fill");
  document
    .getElementsByClassName("playListPlay")
    [index - 1].classList.add("bi-pause-circle-fill");

  // Save current player state
  saveCurrentPlayerState(index.toString(), 0, true);

  // Add to recently played history
  addSongToRecentlyPlayed(index);

  // Update master heart icon
  updateMasterHeartIcon();
};

const random_music = () => {
  masterPlay.classList.add("bi-pause-fill");
  wave.classList.add("active2");
  if (index == songs.length) {
    index == 0;
  }
  index = Math.floor(Math.random() * songs.length + 1);
  music.src = `./audio/${index}.mp3`;
  download_music.href = `./audio/${index}.mp3`;
  poster_master_play.src = `./styles/images/img/${index}.jpg`;
  music.play();
  let song_title = songs.filter((ele) => {
    return ele.id == index;
  });

  song_title.forEach((ele) => {
    let { songName } = ele;
    title.innerHTML = songName;
    download_music.setAttribute("download", extractSongName(songName));
  });
  makeAllBackgrounds();
  Array.from(document.getElementsByClassName("songItem"))[
    `${index - 1}`
  ].style.background = "rgb(105, 105, 170, .1)";
  makeAllPlays();
  document
    .getElementsByClassName("playListPlay")
    [index - 1].classList.remove("bi-play-circle-fill");
  document
    .getElementsByClassName("playListPlay")
    [index - 1].classList.add("bi-pause-circle-fill");

  // Save current player state
  saveCurrentPlayerState(index.toString(), 0, true);

  // Add to recently played history
  addSongToRecentlyPlayed(index);

  // Update master heart icon
  updateMasterHeartIcon();
};

let shuffle = document.getElementsByClassName("shuffle")[0];

shuffle.addEventListener("click", () => {
  let a = shuffle.innerHTML;

  switch (a) {
    case "next":
      shuffle.classList.add("bi-arrow-repeat");
      shuffle.classList.remove("bi-music-note-beamed");
      shuffle.classList.remove("bi-shuffle");
      shuffle.innerHTML = "repeat";
      break;
    case "repeat":
      shuffle.classList.remove("bi-arrow-repeat");
      shuffle.classList.remove("bi-music-note-beamed");
      shuffle.classList.add("bi-shuffle");
      shuffle.innerHTML = "random";
      break;
    case "random":
      shuffle.classList.remove("bi-arrow-repeat");
      shuffle.classList.add("bi-music-note-beamed");
      shuffle.classList.remove("bi-shuffle");
      shuffle.innerHTML = "next";
      break;
  }
});

music.addEventListener("ended", () => {
  let b = shuffle.innerHTML;

  switch (b) {
    case "repeat":
      repeat_music();
      break;
    case "next":
      next_music();
      break;
    case "random":
      random_music();
      break;
  }
});

let vol_icon = document.getElementById("vol_icon");
let vol = document.getElementById("vol");
let vol_dot = document.getElementById("vol_dot");
let vol_bar = document.getElementsByClassName("vol_bar")[0];

vol.addEventListener("change", () => {
  if (vol.value == 0) {
    vol_icon.classList.remove("bi-volume-down-fill");
    vol_icon.classList.add("bi-volume-mute-fill");
    vol_icon.classList.remove("bi-volume-up-fill");
  }
  if (vol.value > 0) {
    vol_icon.classList.add("bi-volume-down-fill");
    vol_icon.classList.remove("bi-volume-mute-fill");
    vol_icon.classList.remove("bi-volume-up-fill");
  }
  if (vol.value > 50) {
    vol_icon.classList.remove("bi-volume-down-fill");
    vol_icon.classList.remove("bi-volume-mute-fill");
    vol_icon.classList.add("bi-volume-up-fill");
  }

  let vol_a = vol.value;
  vol_bar.style.width = `${vol_a}%`;
  vol_dot.style.left = `${vol_a}%`;
  music.volume = vol_a / 100;
});

let back = document.getElementById("back");
let next = document.getElementById("next");

back.addEventListener("click", () => {
  index -= 1;
  if (index < 1) {
    index = Array.from(document.getElementsByClassName("songItem")).length;
  }
  music.src = `./audio/${index}.mp3`;
  download_music.href = `./audio/${index}.mp3`;
  poster_master_play.src = `./styles/images/img/${index}.jpg`;

  // Play and maintain protection
  music
    .play()
    .then(() => {
      // Maintain playback protection during track changes
      if (window.GlobalAudioManager) {
        window.GlobalAudioManager.preservePlayback();
      }
    })
    .catch((error) => {
      console.log("Play prevented:", error);
    });

  let song_title = songs.filter((ele) => {
    return ele.id == index;
  });

  song_title.forEach((ele) => {
    let { songName } = ele;
    title.innerHTML = songName;
    download_music.setAttribute("download", extractSongName(songName));
  });
  makeAllPlays();

  document.getElementById(`${index}`).classList.remove("bi-play-fill");
  document.getElementById(`${index}`).classList.add("bi-pause-fill");
  makeAllBackgrounds();
  Array.from(document.getElementsByClassName("songItem"))[
    `${index - 1}`
  ].style.background = "rgb(105, 105, 170, .1)";

  // Add to recently played history
  addSongToRecentlyPlayed(index);

  // Update master heart icon
  updateMasterHeartIcon();

  // Save current player state
  saveCurrentPlayerState(index.toString(), 0, true);
});

next.addEventListener("click", () => {
  index -= 0;
  index += 1;
  if (index > Array.from(document.getElementsByClassName("songItem")).length) {
    index = 1;
  }
  music.src = `./audio/${index}.mp3`;
  download_music.href = `./audio/${index}.mp3`;
  poster_master_play.src = `./styles/images/img/${index}.jpg`;

  // Play and maintain protection
  music
    .play()
    .then(() => {
      // Maintain playback protection during track changes
      if (window.GlobalAudioManager) {
        window.GlobalAudioManager.preservePlayback();
      }
    })
    .catch((error) => {
      console.log("Play prevented:", error);
    });

  let song_title = songs.filter((ele) => {
    return ele.id == index;
  });

  song_title.forEach((ele) => {
    let { songName } = ele;
    title.innerHTML = songName;
    download_music.setAttribute("download", extractSongName(songName));
  });
  makeAllPlays();

  document.getElementById(`${index}`).classList.remove("bi-play-fill");
  document.getElementById(`${index}`).classList.add("bi-pause-fill");
  makeAllBackgrounds();
  Array.from(document.getElementsByClassName("songItem"))[
    `${index - 1}`
  ].style.background = "rgb(105, 105, 170, .1)";

  // Add to recently played history
  addSongToRecentlyPlayed(index);

  // Update master heart icon
  updateMasterHeartIcon();

  // Save current player state
  saveCurrentPlayerState(index.toString(), 0, true);
});

// Add click event to popular song items
Array.from(document.querySelectorAll(".pop_song .songItem")).forEach(
  (Element) => {
    // Check if heart icon already exists to prevent duplicates
    if (Element.querySelector(".add-to-library")) {
      return;
    }

    // Add library button to song item
    const songId = Element.querySelector(".playListPlay").id;

    // Add library button directly to song item (no container needed)
    const libraryBtn = document.createElement("i");
    libraryBtn.className = "bi add-to-library";
    libraryBtn.setAttribute("data-id", songId);

    // Set appropriate icon based on library status
    if (isSongInLibrary(songId)) {
      libraryBtn.classList.add("bi-heart-fill");
      libraryBtn.title = "Remove from Library";
    } else {
      libraryBtn.classList.add("bi-heart");
      libraryBtn.title = "Add to Library";
    }

    // Add click event to library button
    libraryBtn.addEventListener("click", (e) => {
      e.stopPropagation(); // Prevent navigation to detail page

      // Check if user is logged in
      if (typeof isUserLoggedIn === "function" && !isUserLoggedIn()) {
        if (typeof showLoginRequiredNotification === "function") {
          showLoginRequiredNotification();
        }
        return;
      }

      if (isSongInLibrary(songId)) {
        // Remove from library
        if (removeSongFromLibrary(songId)) {
          // Show notification
          if (typeof showNotification === "function") {
            showNotification("Đã xóa bài hát khỏi thư viện của bạn!", 3000);
          }
          // Sync all heart icons
          syncAllHeartIcons(songId);
        }
      } else {
        // Add to library
        if (addSongToLibrary(songId)) {
          // Show notification
          if (typeof showNotification === "function") {
            showNotification("Thêm vào thư viện thành công", 3000);
          }
          // Sync all heart icons
          syncAllHeartIcons(songId);
        }
      }
    });

    // Add button directly to song item
    Element.appendChild(libraryBtn);

    // Add click event to navigate to music detail page
    Element.addEventListener("click", (e) => {
      // Only navigate if the click is not on the play button or library button
      if (
        !e.target.classList.contains("playListPlay") &&
        !e.target.classList.contains("add-to-library")
      ) {
        const songId = Element.querySelector(".playListPlay").id;
        window.location.href = `music-detail.html?id=${songId}&source=discovery`;
      }
    });
  }
);

// Scroll controls for popular songs
let left_scroll = document.getElementById("left_scroll");
let right_scroll = document.getElementById("right_scroll");
let pop_song = document.getElementsByClassName("pop_song")[0];

left_scroll.addEventListener("click", () => {
  pop_song.scrollLeft -= 330;
});
right_scroll.addEventListener("click", () => {
  pop_song.scrollLeft += 330;
});

// Scroll controls for artists
let left_scrolls = document.getElementById("left_scrolls");
let right_scrolls = document.getElementById("right_scrolls");
let item = document.getElementsByClassName("item")[0];

left_scrolls.addEventListener("click", () => {
  item.scrollLeft -= 330;
});
right_scrolls.addEventListener("click", () => {
  item.scrollLeft += 330;
});
